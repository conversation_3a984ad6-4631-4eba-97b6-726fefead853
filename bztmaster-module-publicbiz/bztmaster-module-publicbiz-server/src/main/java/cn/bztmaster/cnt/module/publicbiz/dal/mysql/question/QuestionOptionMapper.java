package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionOptionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 考题选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionOptionMapper extends BaseMapperX<QuestionOptionDO> {

    default List<QuestionOptionDO> selectListByQuestionId(Long questionId) {
        return selectList(new LambdaQueryWrapperX<QuestionOptionDO>()
                .eq(QuestionOptionDO::getQuestionId, questionId)
                .orderByAsc(QuestionOptionDO::getSortOrder)
                .orderByAsc(QuestionOptionDO::getId));
    }

    default void deleteByQuestionId(Long questionId) {
        delete(new LambdaQueryWrapperX<QuestionOptionDO>()
                .eq(QuestionOptionDO::getQuestionId, questionId));
    }
}
