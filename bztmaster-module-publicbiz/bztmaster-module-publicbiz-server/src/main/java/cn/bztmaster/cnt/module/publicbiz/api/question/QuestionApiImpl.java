package cn.bztmaster.cnt.module.publicbiz.api.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.question.dto.QuestionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 考题管理 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class QuestionApiImpl implements QuestionApi {

    @Resource
    private QuestionService questionService;

    @Resource
    private QuestionConvert questionConvert;

    @Override
    public CommonResult<QuestionRespDTO> getQuestion(Long id) {
        QuestionDO question = questionService.getQuestionDO(id);
        return CommonResult.success(questionConvert.convertDTO(question));
    }

    @Override
    public CommonResult<List<QuestionRespDTO>> getQuestionList(Collection<Long> ids) {
        List<QuestionDO> list = questionService.getQuestionDOList(ids);
        return CommonResult.success(questionConvert.convertDTOList(list));
    }
}
