package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 考题分类新增/更新 Request VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题分类新增/更新 Request VO")
public class QuestionCategorySaveReqVO {

    @Schema(description = "主键ID，更新时必填", example = "1")
    private Long id;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @NotBlank(message = "一级分类名称不能为空")
    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    @NotBlank(message = "一级分类代码不能为空")
    @Schema(description = "一级分类代码", example = "ZY001")
    private String level1Code;

    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    @Schema(description = "二级分类代码", example = "JZ001")
    private String level2Code;

    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    @Schema(description = "三级分类代码", example = "JZFW001")
    private String level3Code;

    @Schema(description = "认定点名称", example = "职业道德基础")
    private String certName;

    @Schema(description = "认定点代码", example = "KP001")
    private String certCode;

    @NotBlank(message = "业务模块不能为空")
    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    @Schema(description = "业务模块名称", example = "家政业务")
    private String bizName;

    @Schema(description = "父级分类ID，0表示顶级分类", example = "0")
    private Long parentId;

    @NotNull(message = "分类层级不能为空")
    @Schema(description = "分类层级：1-一级，2-二级，3-三级", example = "1")
    private Integer level;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
}
