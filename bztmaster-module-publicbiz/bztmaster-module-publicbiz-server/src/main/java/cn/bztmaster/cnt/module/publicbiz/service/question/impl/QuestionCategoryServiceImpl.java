package cn.bztmaster.cnt.module.publicbiz.service.question.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategorySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionCategoryConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionCategoryMapper;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionCategoryService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.QUESTION_CATEGORY_NOT_EXISTS;

/**
 * 考题分类管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionCategoryServiceImpl implements QuestionCategoryService {

    @Resource
    private QuestionCategoryMapper questionCategoryMapper;

    @Resource
    private QuestionCategoryConvert questionCategoryConvert;

    @Override
    public Long createQuestionCategory(QuestionCategorySaveReqVO createReqVO) {
        // 插入
        QuestionCategoryDO questionCategory = questionCategoryConvert.convert(createReqVO);
        questionCategory.setCreateTime(LocalDateTime.now());
        questionCategory.setDeleted(false);
        questionCategoryMapper.insert(questionCategory);
        // 返回
        return questionCategory.getId();
    }

    @Override
    public void updateQuestionCategory(QuestionCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionCategoryExists(updateReqVO.getId());
        // 更新
        QuestionCategoryDO updateObj = questionCategoryConvert.convert(updateReqVO);
        updateObj.setUpdateTime(LocalDateTime.now());
        questionCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionCategory(Long id) {
        // 校验存在
        validateQuestionCategoryExists(id);
        // 删除
        questionCategoryMapper.deleteById(id);
    }

    private void validateQuestionCategoryExists(Long id) {
        if (questionCategoryMapper.selectById(id) == null) {
            throw exception(QUESTION_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public QuestionCategoryRespVO getQuestionCategory(Long id) {
        QuestionCategoryDO questionCategory = questionCategoryMapper.selectById(id);
        return questionCategoryConvert.convert(questionCategory);
    }

    @Override
    public List<QuestionCategoryRespVO> getQuestionCategoryList(QuestionCategoryListReqVO listReqVO) {
        List<QuestionCategoryDO> list = questionCategoryMapper.selectList(listReqVO);
        return questionCategoryConvert.convertList(list);
    }

    // ==================== API 接口需要的方法 ====================

    @Override
    public QuestionCategoryDO getQuestionCategoryDO(Long id) {
        return questionCategoryMapper.selectById(id);
    }

    @Override
    public List<QuestionCategoryDO> getQuestionCategoryDOList(Collection<Long> ids) {
        return questionCategoryMapper.selectBatchIds(ids);
    }
}
