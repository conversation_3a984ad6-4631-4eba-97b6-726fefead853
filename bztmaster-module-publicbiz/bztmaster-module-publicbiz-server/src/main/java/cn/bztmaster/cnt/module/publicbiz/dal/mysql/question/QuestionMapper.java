package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 考题管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionMapper extends BaseMapperX<QuestionDO> {

    default PageResult<QuestionDO> selectPage(QuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDO>()
                .likeIfPresent(QuestionDO::getLevel1Name, reqVO.getLevel1Name())
                .likeIfPresent(QuestionDO::getLevel2Name, reqVO.getLevel2Name())
                .likeIfPresent(QuestionDO::getLevel3Name, reqVO.getLevel3Name())
                .likeIfPresent(QuestionDO::getCertName, reqVO.getCertName())
                .eqIfPresent(QuestionDO::getType, reqVO.getType())
                .eqIfPresent(QuestionDO::getBiz, reqVO.getBiz())
                .likeIfPresent(QuestionDO::getTitle, reqVO.getKeyword())
                .eqIfPresent(QuestionDO::getDifficulty, reqVO.getDifficulty())
                .eqIfPresent(QuestionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(QuestionDO::getCreator, reqVO.getCreator())
                .orderByDesc(QuestionDO::getId));
    }
}
