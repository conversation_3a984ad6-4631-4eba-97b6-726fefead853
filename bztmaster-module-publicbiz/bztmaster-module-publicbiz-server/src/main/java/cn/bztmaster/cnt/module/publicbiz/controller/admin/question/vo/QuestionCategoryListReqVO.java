package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 考题分类管理列表查询 Request VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题分类管理列表查询 Request VO")
public class QuestionCategoryListReqVO {

    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    @Schema(description = "父级分类ID", example = "0")
    private Long parentId;

    @Schema(description = "分类层级：1-一级，2-二级，3-三级", example = "1")
    private Integer level;

    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;
}
