package cn.bztmaster.cnt.module.publicbiz.dal.mysql.question;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 考题分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionCategoryMapper extends BaseMapperX<QuestionCategoryDO> {

    default List<QuestionCategoryDO> selectList(String level1, String biz) {
        return selectList(new LambdaQueryWrapperX<QuestionCategoryDO>()
                .likeIfPresent(QuestionCategoryDO::getLevel1Name, level1)
                .eqIfPresent(QuestionCategoryDO::getBiz, biz)
                .orderByAsc(QuestionCategoryDO::getLevel)
                .orderByAsc(QuestionCategoryDO::getSortOrder)
                .orderByDesc(QuestionCategoryDO::getId));
    }
}
