package cn.bztmaster.cnt.module.publicbiz.controller.admin.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 考题管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 考题管理")
@RestController
@RequestMapping("/publicbiz/question")
@Validated
public class QuestionController {

    @Resource
    private QuestionService questionService;

    @PostMapping("/create")
    @Operation(summary = "新增考题")
    @PreAuthorize("@ss.hasPermission('publicbiz:question:create')")
    public CommonResult<Long> createQuestion(@Valid @RequestBody QuestionSaveReqVO createReqVO) {
        return success(questionService.createQuestion(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新考题")
    @PreAuthorize("@ss.hasPermission('publicbiz:question:update')")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqVO updateReqVO) {
        questionService.updateQuestion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除考题")
    @Parameter(name = "id", description = "考题编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:question:delete')")
    public CommonResult<Boolean> deleteQuestion(@RequestParam("id") Long id) {
        questionService.deleteQuestion(id);
        return success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得考题详情")
    @Parameter(name = "id", description = "考题编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:question:query')")
    public CommonResult<QuestionRespVO> getQuestion(@RequestParam("id") Long id) {
        QuestionDO question = questionService.getQuestion(id);
        return success(QuestionConvert.INSTANCE.convert(question));
    }

    @GetMapping("/page")
    @Operation(summary = "获得考题分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:question:query')")
    public CommonResult<PageResult<QuestionRespVO>> getQuestionPage(@Valid QuestionPageReqVO pageReqVO) {
        PageResult<QuestionDO> pageResult = questionService.getQuestionPage(pageReqVO);
        return success(QuestionConvert.INSTANCE.convertPage(pageResult));
    }

    // 考题分类相关接口
    @PostMapping("/category/create")
    @Operation(summary = "新增考题分类")
    @PreAuthorize("@ss.hasPermission('publicbiz:question:category:create')")
    public CommonResult<Long> createQuestionCategory(@Valid @RequestBody QuestionCategorySaveReqVO createReqVO) {
        return success(questionService.createQuestionCategory(createReqVO));
    }

    @DeleteMapping("/category/delete")
    @Operation(summary = "删除考题分类")
    @Parameter(name = "id", description = "分类编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:question:category:delete')")
    public CommonResult<Boolean> deleteQuestionCategory(@RequestParam("id") Long id) {
        questionService.deleteQuestionCategory(id);
        return success(true);
    }

    @GetMapping("/category/list")
    @Operation(summary = "获得考题分类列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:question:category:query')")
    public CommonResult<PageResult<QuestionCategoryRespVO>> getQuestionCategoryList(
            @RequestParam(value = "level1", required = false) String level1,
            @RequestParam(value = "biz", required = false) String biz) {
        return success(questionService.getQuestionCategoryList(level1, biz));
    }
}
