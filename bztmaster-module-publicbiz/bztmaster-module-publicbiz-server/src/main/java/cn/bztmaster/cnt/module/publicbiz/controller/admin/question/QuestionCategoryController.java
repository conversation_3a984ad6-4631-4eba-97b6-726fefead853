package cn.bztmaster.cnt.module.publicbiz.controller.admin.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 考题分类管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/publicbiz/question/category")
@Tag(name = "资源中心-考题分类管理")
public class QuestionCategoryController {

    @Resource
    private QuestionCategoryService questionCategoryService;

    @PostMapping("/create")
    @Operation(summary = "新增分类")
    public CommonResult<Long> createQuestionCategory(@Valid @RequestBody QuestionCategorySaveReqVO createReqVO) {
        return CommonResult.success(questionCategoryService.createQuestionCategory(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑分类")
    public CommonResult<Boolean> updateQuestionCategory(@Valid @RequestBody QuestionCategorySaveReqVO updateReqVO) {
        questionCategoryService.updateQuestionCategory(updateReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除分类")
    public CommonResult<Boolean> deleteQuestionCategory(@RequestParam("id") Long id) {
        questionCategoryService.deleteQuestionCategory(id);
        return CommonResult.success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "分类详情")
    public CommonResult<QuestionCategoryRespVO> getQuestionCategory(@RequestParam("id") Long id) {
        QuestionCategoryRespVO category = questionCategoryService.getQuestionCategory(id);
        return CommonResult.success(category);
    }

    @GetMapping("/list")
    @Operation(summary = "查询分类列表")
    public CommonResult<List<QuestionCategoryRespVO>> getQuestionCategoryList(@Valid QuestionCategoryListReqVO listReqVO) {
        List<QuestionCategoryRespVO> list = questionCategoryService.getQuestionCategoryList(listReqVO);
        return CommonResult.success(list);
    }
}
