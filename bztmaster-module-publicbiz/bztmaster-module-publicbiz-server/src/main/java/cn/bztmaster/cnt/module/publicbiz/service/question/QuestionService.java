package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 考题管理 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionService {

    /**
     * 创建考题
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestion(@Valid QuestionSaveReqVO createReqVO);

    /**
     * 更新考题
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestion(@Valid QuestionSaveReqVO updateReqVO);

    /**
     * 删除考题
     *
     * @param id 编号
     */
    void deleteQuestion(Long id);

    /**
     * 获得考题
     *
     * @param id 编号
     * @return 考题
     */
    QuestionDO getQuestion(Long id);

    /**
     * 获得考题列表
     *
     * @param ids 编号
     * @return 考题列表
     */
    List<QuestionDO> getQuestionList(Collection<Long> ids);

    /**
     * 获得考题分页
     *
     * @param pageReqVO 分页查询
     * @return 考题分页
     */
    PageResult<QuestionDO> getQuestionPage(QuestionPageReqVO pageReqVO);

    // ==================== 考题分类相关方法 ====================

    /**
     * 创建考题分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionCategory(@Valid QuestionCategorySaveReqVO createReqVO);

    /**
     * 删除考题分类
     *
     * @param id 编号
     */
    void deleteQuestionCategory(Long id);

    /**
     * 获得考题分类列表
     *
     * @param level1 一级分类名称
     * @param biz    业务模块
     * @return 考题分类列表
     */
    PageResult<QuestionCategoryRespVO> getQuestionCategoryList(String level1, String biz);
}
