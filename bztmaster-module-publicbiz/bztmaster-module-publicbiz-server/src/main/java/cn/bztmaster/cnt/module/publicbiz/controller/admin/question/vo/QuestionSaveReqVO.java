package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 考题管理保存 Request VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题管理保存 Request VO")
public class QuestionSaveReqVO {

    @Schema(description = "主键ID，新增时不传", example = "1")
    private Long id;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @NotBlank(message = "一级分类名称不能为空")
    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    @NotBlank(message = "一级分类代码不能为空")
    @Schema(description = "一级分类代码", example = "ZY001")
    private String level1Code;

    @NotBlank(message = "二级分类名称不能为空")
    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    @NotBlank(message = "二级分类代码不能为空")
    @Schema(description = "二级分类代码", example = "JZ001")
    private String level2Code;

    @NotBlank(message = "三级分类名称不能为空")
    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    @NotBlank(message = "三级分类代码不能为空")
    @Schema(description = "三级分类代码", example = "JZFW001")
    private String level3Code;

    @NotBlank(message = "认定点名称不能为空")
    @Schema(description = "认定点名称", example = "职业道德基础")
    private String certName;

    @NotBlank(message = "认定点代码不能为空")
    @Schema(description = "认定点代码", example = "KP001")
    private String certCode;

    @NotBlank(message = "题干内容不能为空")
    @Schema(description = "题干内容")
    private String title;

    @NotBlank(message = "题型不能为空")
    @Schema(description = "题型", example = "单选题")
    private String type;

    @NotBlank(message = "参考答案不能为空")
    @Schema(description = "参考答案")
    private String answer;

    @NotBlank(message = "业务模块不能为空")
    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    @Schema(description = "业务模块名称", example = "家政业务")
    private String bizName;

    @Schema(description = "难度等级：1-简单，2-中等，3-困难", example = "1")
    private Integer difficulty;

    @Schema(description = "题目分值", example = "5.00")
    private BigDecimal score;

    @Schema(description = "选项列表（适用于所有题型）")
    @Valid
    private List<QuestionOptionSaveReqVO> options;
}