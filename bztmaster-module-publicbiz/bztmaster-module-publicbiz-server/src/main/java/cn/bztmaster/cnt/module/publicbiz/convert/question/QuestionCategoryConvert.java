package cn.bztmaster.cnt.module.publicbiz.convert.question;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategoryRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionCategorySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 考题分类管理 Convert
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@Component
public interface QuestionCategoryConvert {

    QuestionCategoryConvert INSTANCE = Mappers.getMapper(QuestionCategoryConvert.class);

    QuestionCategoryDO convert(QuestionCategorySaveReqVO bean);

    QuestionCategoryRespVO convert(QuestionCategoryDO bean);

    List<QuestionCategoryRespVO> convertList(List<QuestionCategoryDO> list);
}
