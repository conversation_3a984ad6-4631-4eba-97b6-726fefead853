package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考题选项管理 Response VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题选项管理 Response VO")
public class QuestionOptionRespVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @Schema(description = "考题ID", example = "1")
    private Long questionId;

    @Schema(description = "选项类型：choice-选择项，match_left-匹配左列，match_right-匹配右列", example = "choice")
    private String optionType;

    @Schema(description = "选项标识，如：A、B、C、D或1、2、3、4", example = "A")
    private String optionKey;

    @Schema(description = "选项内容", example = "诚实守信")
    private String optionContent;

    @Schema(description = "是否正确答案：0-否，1-是", example = "1")
    private Integer isCorrect;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "匹配目标，用于匹配题记录对应关系", example = "1")
    private String matchTarget;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "admin")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
